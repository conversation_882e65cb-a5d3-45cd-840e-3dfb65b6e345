/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Notion-style Black & Yellow Color Palette */
    --primary-black: #191919;
    --secondary-black: #2f2f2f;
    --light-black: #3f3f3f;
    --primary-yellow: #ffeb3b;
    --secondary-yellow: #fff176;
    --dark-yellow: #f9a825;
    --accent-yellow: #ffcc02;

    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: #e0e0e0;
    --text-muted: #bdbdbd;
    --text-dark: #191919;

    /* Background Colors */
    --bg-primary: #191919;
    --bg-secondary: #2f2f2f;
    --bg-tertiary: #3f3f3f;
    --bg-light: #f5f5f5;

    /* Border Colors */
    --border-primary: #404040;
    --border-secondary: #555555;
    --border-yellow: #ffeb3b;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, 'Apple Color Emoji', Arial, sans-serif, 'Segoe UI Emoji', 'Segoe UI Symbol';
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
}

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles for accessibility */
*:focus {
    outline: 2px solid var(--primary-yellow);
    outline-offset: 2px;
}



/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 40px 0 60px;
    box-sizing: border-box;
}

/* Header */
.header {
    background: var(--bg-secondary);
    box-shadow: 0 2px 20px rgba(255, 235, 59, 0.1);
    position: relative;
    border-bottom: 1px solid var(--border-primary);
}

.navbar {
    padding: 1rem 0;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    gap: 1rem;
    min-height: 60px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 1.2rem;
    flex: 1;
    justify-content: center;
    margin: 0;
    padding: 0;
}

.nav-right {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-shrink: 0;
    min-width: fit-content;
}

.logo h1 {
    color: var(--primary-yellow);
    font-size: 1.8em;
    margin-bottom: 0.1em;
    font-weight: 700;
    letter-spacing: -0.5px;
}

.tagline {
    color: var(--text-primary);
    font-size: 0.85em;
    margin-bottom: 0.2em;
    font-weight: 500;
}

.license-info {
    color: var(--text-secondary);
    font-size: 0.8em;
    font-style: italic;
}



.nav-menu a {
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 500;
    padding: 0.5rem 0.8rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    border: 1px solid transparent;
    white-space: nowrap;
    font-size: 0.95em;
}

.nav-menu a:hover,
.nav-menu a[aria-current="page"] {
    background: var(--primary-yellow);
    color: var(--text-dark);
    border: 1px solid var(--dark-yellow);
    transform: translateY(-1px);
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: 1px solid var(--border-primary);
    color: var(--text-primary);
    font-size: 1.5em;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.mobile-menu-toggle:hover {
    background: var(--bg-tertiary);
    border-color: var(--primary-yellow);
}

/* Auth Buttons */
.auth-buttons {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    flex-shrink: 0;
}

.btn-auth {
    text-decoration: none;
    padding: 0.5rem 0.8rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 1px solid transparent;
    white-space: nowrap;
    font-size: 0.9em;
    min-width: fit-content;
}

.btn-login {
    color: var(--text-secondary);
    border: 1px solid var(--border-primary);
}

.btn-login:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border-color: var(--primary-yellow);
}

.btn-signup {
    background: var(--primary-yellow);
    color: var(--text-dark);
    border: 1px solid var(--dark-yellow);
}

.btn-signup:hover {
    background: var(--dark-yellow);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(255, 235, 59, 0.3);
}

/* Main Content */
.main-content {
    min-height: calc(100vh - 200px);
}

/* Login & Signup Sections */
.login-section,
.signup-section {
    padding: 3rem 0;
    background: var(--bg-primary);
}

.login-container,
.signup-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    max-width: 1200px;
    margin: 0 auto;
    align-items: start;
}

.login-form-wrapper,
.signup-form-wrapper {
    background: var(--bg-secondary);
    padding: 2.5rem;
    border-radius: 12px;
    border: 1px solid var(--border-primary);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.form-header {
    text-align: center;
    margin-bottom: 2rem;
}

.form-header h2 {
    color: var(--primary-yellow);
    font-size: 1.8em;
    margin-bottom: 0.5rem;
}

.form-header p {
    color: var(--text-secondary);
    font-size: 1em;
}

.login-form,
.signup-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    color: var(--text-primary);
    font-weight: 500;
    font-size: 0.95em;
}

.form-group input {
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 1em;
    transition: all 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-yellow);
    box-shadow: 0 0 0 2px rgba(255, 235, 59, 0.2);
}

.form-note {
    color: var(--text-secondary);
    font-size: 0.85em;
    font-style: italic;
}

.checkbox-group {
    flex-direction: row;
    align-items: flex-start;
    gap: 0.75rem;
}

.checkbox-wrapper {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.95em;
    line-height: 1.4;
}

.checkbox-wrapper input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0.5rem 0;
}

.forgot-password {
    color: var(--primary-yellow);
    text-decoration: none;
    font-size: 0.9em;
}

.forgot-password:hover {
    text-decoration: underline;
}

.login-btn,
.signup-btn {
    padding: 1rem 2rem;
    font-size: 1.1em;
    font-weight: 600;
    margin-top: 1rem;
}

.form-footer {
    text-align: center;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-primary);
}

.signup-link,
.login-link {
    color: var(--primary-yellow);
    text-decoration: none;
    font-weight: 500;
}

.signup-link:hover,
.login-link:hover {
    text-decoration: underline;
}

.login-info,
.signup-info {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.verification-notice,
.responsible-gaming-notice {
    background: var(--bg-secondary);
    padding: 1.5rem;
    border-radius: 8px;
    border-left: 4px solid var(--primary-yellow);
}

.verification-notice h4,
.responsible-gaming-notice h4 {
    color: var(--primary-yellow);
    margin-bottom: 0.5rem;
    font-size: 1em;
}

.help-link {
    color: var(--primary-yellow);
    text-decoration: none;
    font-weight: 500;
    display: inline-block;
    margin-top: 0.5rem;
}

.help-link:hover {
    text-decoration: underline;
}

/* Page Headers */
.page-header-section {
    padding: 3rem 0;
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
    border-bottom: 3px solid var(--primary-yellow);
}

.page-header {
    text-align: center;
    padding: 0;
    background: none;
    color: var(--text-primary);
    margin-bottom: 0;
    border-bottom: none;
}

.page-header h1 {
    font-size: 2.5em;
    margin-bottom: 0.5em;
    color: var(--primary-yellow);
    font-weight: 700;
}

.page-subtitle {
    font-size: 1.2em;
    opacity: 0.9;
    color: var(--text-secondary);
}

.last-updated,
.verification-notice,
.important-notice {
    background: rgba(255, 235, 59, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 8px;
    margin-top: 1rem;
    display: inline-block;
    border: 1px solid var(--primary-yellow);
    color: var(--primary-yellow);
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    color: var(--text-primary);
    padding: 4rem 0;
    text-align: center;
    border-bottom: 3px solid var(--primary-yellow);
    position: relative;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 50% 50%, rgba(255, 235, 59, 0.1) 0%, transparent 70%);
    pointer-events: none;
}

.hero-content {
    position: relative;
    z-index: 1;
}

.hero-content h2 {
    font-size: 3em;
    margin-bottom: 1rem;
    font-weight: 700;
    color: var(--primary-yellow);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
    font-size: 1.3em;
    margin-bottom: 2rem;
    opacity: 0.9;
    color: var(--text-secondary);
}

.hero-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
    max-width: 900px;
    margin-left: auto;
    margin-right: auto;
}

.feature {
    background: rgba(255, 235, 59, 0.1);
    padding: 1.5rem;
    border-radius: 12px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 235, 59, 0.2);
    transition: all 0.3s ease;
}

.feature:hover {
    background: rgba(255, 235, 59, 0.15);
    transform: translateY(-5px);
    border-color: var(--primary-yellow);
}

.feature h3 {
    font-size: 1.3em;
    margin-bottom: 0.5rem;
    color: var(--primary-yellow);
    font-weight: 600;
}

.feature p {
    color: var(--text-secondary);
}

.cta-section {
    margin-top: 3rem;
}

.cta-button {
    background: var(--primary-yellow);
    color: var(--text-dark);
    border: none;
    padding: 1rem 2rem;
    font-size: 1.2em;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 20px rgba(255, 235, 59, 0.3);
    font-weight: 600;
    border: 2px solid var(--dark-yellow);
}

.cta-button:hover {
    background: var(--secondary-yellow);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 235, 59, 0.4);
}

.disclaimer {
    margin-top: 1rem;
    font-size: 0.9em;
    opacity: 0.8;
}

/* Info Blocks */
.info-blocks {
    padding: 4rem 0;
    background: var(--bg-secondary);
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.info-card {
    background: var(--bg-tertiary);
    padding: 2rem;
    border-radius: 12px;
    border-left: 4px solid var(--primary-yellow);
    transition: all 0.3s ease;
    border: 1px solid var(--border-primary);
}

.info-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-yellow);
    box-shadow: 0 8px 25px rgba(255, 235, 59, 0.2);
}

.info-card h3 {
    color: var(--primary-yellow);
    margin-bottom: 1rem;
    font-size: 1.3em;
    font-weight: 600;
}

.info-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Buttons */
.btn-primary {
    background: var(--primary-yellow);
    color: var(--text-dark);
    border: 2px solid var(--dark-yellow);
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    font-size: 1em;
    font-weight: 600;
}

.btn-primary:hover {
    background: var(--secondary-yellow);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 235, 59, 0.3);
}

.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 2px solid var(--border-primary);
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    font-size: 1em;
    font-weight: 500;
}

.btn-secondary:hover {
    background: var(--bg-secondary);
    border-color: var(--primary-yellow);
    color: var(--primary-yellow);
    transform: translateY(-2px);
}

/* Modals */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    backdrop-filter: blur(5px);
}

.modal-content {
    background: var(--bg-secondary);
    padding: 2rem;
    border-radius: 16px;
    max-width: 500px;
    width: 90%;
    text-align: center;
    border: 2px solid var(--primary-yellow);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

.modal-content h2 {
    color: var(--primary-yellow);
    margin-bottom: 1rem;
    font-weight: 700;
}

.modal-content p {
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.modal-content p strong {
    color: var(--text-secondary);
}

.modal-content form {
    margin-top: 1.5rem;
}

.modal-content label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    text-align: left;
    color: var(--text-primary);
}

.modal-content input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--border-primary);
    border-radius: 8px;
    margin-bottom: 1rem;
    font-size: 1em;
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.modal-content input:focus {
    border-color: var(--primary-yellow);
    outline: none;
}

.modal-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 1.5rem;
}

/* Age Confirmation Styles */
.age-confirmation {
    text-align: center;
}

.age-confirmation p {
    margin-bottom: 1rem;
    line-height: 1.6;
    color: var(--text-primary);
}

.age-confirmation p strong {
    color: var(--text-primary);
}

/* Specific fix for age confirmation modal text visibility */
.age-confirmation {
    color: var(--text-primary);
}

.age-confirmation * {
    color: inherit;
}

.warning-text {
    color: var(--text-dark) !important;
    font-size: 0.9em;
    font-style: italic;
    background: var(--primary-yellow);
    padding: 0.75rem;
    border-radius: 8px;
    border-left: 4px solid var(--dark-yellow);
    font-weight: 500;
}

/* Ensure all text on yellow backgrounds is dark */
.warning-text,
.warning-text *,
.btn-primary,
.btn-primary *,
.cta-button,
.cta-button *,
.help-links a,
.help-links a *,
.step-number,
.step-number *,
.number,
.number *,
.supp-number,
.supp-number * {
    color: var(--text-dark) !important;
}

/* Cookie Banner */
.cookie-banner {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--bg-secondary);
    color: var(--text-primary);
    padding: 1rem;
    z-index: 1000;
    box-shadow: 0 -4px 20px rgba(255, 235, 59, 0.1);
    border-top: 2px solid var(--primary-yellow);
}

.cookie-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    gap: 1rem;
}

.cookie-content a {
    color: var(--primary-yellow);
    text-decoration: none;
    font-weight: 500;
}

.cookie-content a:hover {
    text-decoration: underline;
}

.cookie-buttons {
    display: flex;
    gap: 1rem;
}

/* Responsible Gaming */
.responsible-gaming-highlight {
    background: var(--bg-tertiary);
    padding: 3rem 0;
    text-align: center;
    border-top: 2px solid var(--primary-yellow);
    border-bottom: 2px solid var(--primary-yellow);
}

.responsible-gaming-highlight h3 {
    color: var(--primary-yellow);
    font-size: 1.8em;
    margin-bottom: 1rem;
    font-weight: 700;
}

.responsible-gaming-highlight p {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
}

.help-links {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
    margin-top: 1.5rem;
}

.help-links a {
    background: var(--primary-yellow);
    color: var(--text-dark);
    padding: 0.75rem 1.5rem;
    text-decoration: none;
    border-radius: 25px;
    transition: all 0.3s ease;
    font-weight: 600;
    border: 2px solid var(--dark-yellow);
}

.help-links a:hover {
    background: var(--secondary-yellow);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(255, 235, 59, 0.3);
}

/* How It Works Section */
.how-it-works-section {
    padding: 4rem 0;
    background-color: var(--bg-secondary);
    border-top: 1px solid var(--border-primary);
}

.how-it-works-section h2 {
    text-align: center;
    margin-bottom: 3rem;
    color: var(--primary-yellow);
    font-size: 2.5rem;
    font-weight: 700;
}

.lottery-explanation {
    margin-bottom: 4rem;
}

.lottery-step {
    display: flex;
    align-items: flex-start;
    margin-bottom: 2.5rem;
    padding: 2rem;
    background-color: var(--bg-tertiary);
    border-radius: 12px;
    border-left: 4px solid var(--primary-yellow);
}

.step-number {
    background-color: var(--primary-yellow);
    color: var(--text-dark);
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.25rem;
    margin-right: 1.5rem;
    flex-shrink: 0;
}

.step-content h3 {
    color: var(--primary-yellow);
    margin-bottom: 0.75rem;
    font-size: 1.25rem;
}

.step-content p {
    color: var(--text-secondary);
    line-height: 1.6;
}

.lottery-details h3 {
    text-align: center;
    margin-bottom: 2rem;
    color: var(--primary-yellow);
    font-size: 2rem;
}

.game-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.game-info-card {
    background-color: var(--bg-tertiary);
    padding: 2rem;
    border-radius: 12px;
    border: 1px solid var(--border-primary);
    border-top: 3px solid var(--primary-yellow);
}

.game-info-card h4 {
    color: var(--primary-yellow);
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.game-info-card p {
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.game-info-card strong {
    color: var(--text-primary);
}

/* Enhanced Responsible Gaming Section */
.responsible-gaming-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: start;
}

.rg-main h3 {
    color: var(--primary-yellow);
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.warning-box {
    background-color: var(--bg-tertiary);
    border: 2px solid var(--dark-yellow);
    border-radius: 12px;
    padding: 2rem;
    margin-top: 2rem;
}

.warning-box h4 {
    color: var(--dark-yellow);
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.warning-box ul {
    list-style: none;
    padding: 0;
}

.warning-box li {
    color: var(--text-secondary);
    margin-bottom: 0.75rem;
    padding-left: 1.5rem;
    position: relative;
}

.warning-box li::before {
    content: "⚠️";
    position: absolute;
    left: 0;
    top: 0;
}

.help-resources h3 {
    color: var(--primary-yellow);
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

.help-links {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
}

.help-link {
    display: block;
    background-color: var(--bg-tertiary);
    padding: 1.5rem;
    border-radius: 8px;
    border-left: 4px solid var(--primary-yellow);
    text-decoration: none;
    transition: all 0.3s ease;
}

.help-link:hover {
    background-color: var(--light-black);
    transform: translateX(5px);
}

.help-link strong {
    color: var(--primary-yellow);
    display: block;
    margin-bottom: 0.25rem;
    font-size: 1.1rem;
}

.help-link span {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.self-assessment {
    background-color: var(--bg-tertiary);
    padding: 2rem;
    border-radius: 12px;
    border: 1px solid var(--border-primary);
}

.self-assessment h4 {
    color: var(--primary-yellow);
    margin-bottom: 1rem;
}

.self-assessment ul {
    list-style: none;
    padding: 0;
    margin-bottom: 1rem;
}

.self-assessment li {
    color: var(--text-secondary);
    margin-bottom: 0.75rem;
    padding-left: 1.5rem;
    position: relative;
}

.self-assessment li::before {
    content: "?";
    position: absolute;
    left: 0;
    top: 0;
    color: var(--primary-yellow);
    font-weight: bold;
}

/* Legal Compliance Section */
.legal-compliance-section {
    padding: 4rem 0;
    background-color: var(--bg-primary);
    border-top: 1px solid var(--border-primary);
}

.legal-compliance-section h2 {
    text-align: center;
    margin-bottom: 3rem;
    color: var(--primary-yellow);
    font-size: 2.5rem;
    font-weight: 700;
}

.compliance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.compliance-card {
    background-color: var(--bg-tertiary);
    padding: 2.5rem;
    border-radius: 12px;
    border: 1px solid var(--border-primary);
    border-top: 4px solid var(--primary-yellow);
}

.compliance-card h3 {
    color: var(--primary-yellow);
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.compliance-card ul {
    list-style: none;
    padding: 0;
    margin: 1rem 0;
}

.compliance-card li {
    color: var(--text-secondary);
    margin-bottom: 0.75rem;
    padding-left: 1.5rem;
    position: relative;
}

.compliance-card li::before {
    content: "✓";
    position: absolute;
    left: 0;
    top: 0;
    color: var(--primary-yellow);
    font-weight: bold;
}

.compliance-card .warning {
    background-color: var(--bg-secondary);
    border: 2px solid var(--dark-yellow);
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
    color: var(--text-primary);
}

/* Terms Summary Section */
.terms-summary-section {
    padding: 4rem 0;
    background-color: var(--bg-secondary);
    border-top: 1px solid var(--border-primary);
}

.terms-summary-section h2 {
    text-align: center;
    margin-bottom: 3rem;
    color: var(--primary-yellow);
    font-size: 2.5rem;
    font-weight: 700;
}

.terms-intro {
    background-color: var(--bg-tertiary);
    padding: 2rem;
    border-radius: 12px;
    border-left: 4px solid var(--primary-yellow);
    margin-bottom: 3rem;
    text-align: center;
}

.terms-link,
.privacy-link {
    color: var(--primary-yellow);
    text-decoration: none;
    font-weight: 600;
    border-bottom: 2px solid transparent;
    transition: border-color 0.3s ease;
}

.terms-link:hover,
.privacy-link:hover {
    border-bottom-color: var(--primary-yellow);
}

.terms-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.terms-section {
    background-color: var(--bg-tertiary);
    padding: 2rem;
    border-radius: 12px;
    border: 1px solid var(--border-primary);
}

.terms-section h3 {
    color: var(--primary-yellow);
    margin-bottom: 1.5rem;
    font-size: 1.25rem;
    border-bottom: 2px solid var(--primary-yellow);
    padding-bottom: 0.5rem;
}

.terms-section ul {
    list-style: none;
    padding: 0;
}

.terms-section li {
    color: var(--text-secondary);
    margin-bottom: 0.75rem;
    padding-left: 1.5rem;
    position: relative;
}

.terms-section li::before {
    content: "•";
    position: absolute;
    left: 0;
    top: 0;
    color: var(--primary-yellow);
    font-weight: bold;
}

.terms-disclaimer {
    background-color: var(--bg-primary);
    border: 2px solid var(--dark-yellow);
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
}

/* Privacy Policy Section */
.privacy-summary-section {
    padding: 4rem 0;
    background-color: var(--bg-primary);
    border-top: 1px solid var(--border-primary);
}

.privacy-summary-section h2 {
    text-align: center;
    margin-bottom: 3rem;
    color: var(--primary-yellow);
    font-size: 2.5rem;
    font-weight: 700;
}

.privacy-intro {
    background-color: var(--bg-tertiary);
    padding: 2rem;
    border-radius: 12px;
    border-left: 4px solid var(--primary-yellow);
    margin-bottom: 3rem;
    text-align: center;
}

.privacy-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.privacy-section {
    background-color: var(--bg-tertiary);
    padding: 2rem;
    border-radius: 12px;
    border: 1px solid var(--border-primary);
    color: white !important;
}

.privacy-section * {
    color: white !important;
}

.privacy-section h3 {
    color: var(--primary-yellow) !important;
}

.privacy-section h3 {
    color: var(--primary-yellow);
    margin-bottom: 1.5rem;
    font-size: 1.25rem;
    border-bottom: 2px solid var(--primary-yellow);
    padding-bottom: 0.5rem;
}

.privacy-section ul {
    list-style: none;
    padding: 0;
}

.privacy-section li {
    color: white !important;
    margin-bottom: 0.75rem;
    padding-left: 1.5rem;
    position: relative;
}

.privacy-section li strong {
    color: white !important;
}

.privacy-section li::before {
    content: "•";
    position: absolute;
    left: 0;
    top: 0;
    color: var(--primary-yellow);
    font-weight: bold;
}

.privacy-security {
    background-color: var(--bg-secondary);
    border: 2px solid var(--primary-yellow);
    border-radius: 12px;
    padding: 2rem;
}

.privacy-security h3 {
    color: var(--primary-yellow) !important;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.privacy-security a {
    color: var(--primary-yellow);
    text-decoration: none;
}

.privacy-security a:hover {
    text-decoration: underline;
}

/* Footer */
.footer {
    background: var(--bg-secondary);
    color: var(--text-primary);
    padding: 3rem 0 1rem;
    margin-top: 4rem;
    border-top: 3px solid var(--primary-yellow);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    color: var(--primary-yellow);
    margin-bottom: 1rem;
    font-size: 1.2em;
    font-weight: 600;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: var(--primary-yellow);
}

.footer-section p {
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.footer-section strong {
    color: var(--text-primary);
}

.footer-bottom {
    border-top: 1px solid var(--border-primary);
    padding-top: 1rem;
    text-align: center;
    color: var(--text-secondary);
    font-size: 0.9em;
}

.footer-bottom p {
    margin-bottom: 0.5rem;
}

.footer-bottom strong {
    color: var(--primary-yellow);
}

/* Regulator Icons */
.regulator-icons {
    text-align: center;
    padding: 2rem 0;
    border-top: 1px solid var(--border-color);
    border-bottom: 1px solid var(--border-color);
    margin: 2rem 0;
}

.regulator-icons h4 {
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    font-size: 1.2em;
}

.regulator-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1.5rem;
    max-width: 600px;
    margin: 0 auto;
}

.regulator-grid a {
    display: block;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 2px solid var(--border-color);
}

.regulator-grid a:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.regulator-icon {
    width: 100%;
    height: auto;
    max-width: 80px;
    max-height: 60px;
    object-fit: contain;
    display: block;
    margin: 0 auto;
}

@media (max-width: 768px) {
    .regulator-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .regulator-grid a {
        padding: 0.75rem;
    }

    .regulator-icon {
        max-width: 60px;
        max-height: 45px;
    }
}

/* Medium screens */
@media (max-width: 1024px) {
    .nav-container {
        gap: 0.75rem;
    }

    .nav-menu {
        gap: 1rem;
    }

    .nav-menu a {
        padding: 0.4rem 0.6rem;
        font-size: 0.9em;
    }

    .btn-auth {
        padding: 0.4rem 0.7rem;
        font-size: 0.85em;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-container {
        gap: 0.5rem;
        padding: 0 15px;
    }

    .nav-menu {
        display: none;
    }

    .nav-right {
        gap: 0.5rem;
    }

    .auth-buttons {
        gap: 0.25rem;
    }

    .btn-auth {
        padding: 0.4rem 0.6rem;
        font-size: 0.85em;
    }

    .mobile-menu-toggle {
        display: block;
    }
}

    /* Login & Signup Mobile */
    .login-container,
    .signup-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .login-form-wrapper,
    .signup-form-wrapper {
        padding: 1.5rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .form-options {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    /* How It Works Section Mobile */
    .how-it-works-section h2 {
        font-size: 2rem;
    }

    .lottery-step {
        flex-direction: column;
        text-align: center;
    }

    .step-number {
        margin-right: 0;
        margin-bottom: 1rem;
    }

    .game-info-grid {
        grid-template-columns: 1fr;
    }

    /* Responsible Gaming Mobile */
    .responsible-gaming-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .help-links {
        gap: 0.75rem;
    }

    .help-link {
        padding: 1rem;
    }

    /* Legal Compliance Mobile */
    .legal-compliance-section h2 {
        font-size: 2rem;
    }

    .compliance-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .compliance-card {
        padding: 1.5rem;
    }

    /* Terms Summary Mobile */
    .terms-summary-section h2 {
        font-size: 2rem;
    }

    .terms-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .terms-section {
        padding: 1.5rem;
    }

    /* Privacy Policy Mobile */
    .privacy-summary-section h2 {
        font-size: 2rem;
    }

    .privacy-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .privacy-section {
        padding: 1.5rem;
    }

    .privacy-security {
        padding: 1.5rem;
    }

    .hero-content h2 {
        font-size: 2em;
    }

    .hero-features {
        grid-template-columns: 1fr;
    }

    .cookie-content {
        flex-direction: column;
        text-align: center;
    }

    .help-links {
        flex-direction: column;
        align-items: center;
    }

    .modal-buttons {
        flex-direction: column;
    }


}

@media (max-width: 768px) {
    .container {
        padding: 0 30px 0 45px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 25px 0 35px;
    }

    .hero {
        padding: 2rem 0;
    }

    .hero-content h2 {
        font-size: 1.8em;
    }

    .page-header {
        padding: 2rem 0;
    }

    .page-header h1 {
        font-size: 2em;
    }
}

/* Print Styles */
@media print {

    .cookie-banner,
    .modal-overlay,
    .cta-button {
        display: none !important;
    }

    body {
        background: white !important;
        color: black !important;
    }

    .hero,
    .page-header {
        background: white !important;
        color: black !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {

    .hero,
    .page-header {
        background: #000 !important;
        color: #fff !important;
    }

    .btn-primary {
        background: #000 !important;
        border: 2px solid #fff !important;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    html {
        scroll-behavior: auto;
    }
}

/* Process Steps (How It Works) */
.process-steps {
    padding: 3rem 0;
    background: var(--bg-secondary);
}

.steps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.step {
    background: var(--bg-tertiary);
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(255, 235, 59, 0.1);
    text-align: center;
    position: relative;
    transition: all 0.3s ease;
    border: 1px solid var(--border-primary);
}

.step:hover {
    transform: translateY(-8px);
    border-color: var(--primary-yellow);
    box-shadow: 0 12px 30px rgba(255, 235, 59, 0.2);
}

.step-number {
    background: var(--primary-yellow);
    color: var(--text-dark);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5em;
    font-weight: bold;
    margin: 0 auto 1rem;
    border: 2px solid var(--dark-yellow);
}

.step h3 {
    color: var(--primary-yellow);
    margin-bottom: 1rem;
    font-size: 1.3em;
    font-weight: 600;
}

.step p {
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.step ul {
    text-align: left;
    margin-top: 1rem;
}

.step ul li {
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

/* Legal Framework */
.legal-framework,
.age-verification,
.transparency {
    padding: 3rem 0;
    background: #f8f9fa;
}

.regulation-grid,
.transparency-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.regulation-card,
.transparency-item {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    border-left: 4px solid #28a745;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.regulation-card h3,
.transparency-item h3 {
    color: #28a745;
    margin-bottom: 1rem;
}

/* Age Verification Section */
.verification-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    margin-top: 2rem;
}

.verification-warning {
    background: #fff3cd;
    border: 2px solid #ffeaa7;
    border-radius: 10px;
    padding: 1.5rem;
}

.verification-warning h3 {
    color: #856404;
    margin-bottom: 1rem;
}

.verification-warning p {
    color: #856404;
    margin: 0;
}

/* Age Verification Lists */
.verification-info ul {
    list-style: none;
    padding: 0;
    margin: 1rem 0;
}

.verification-info li {
    color: var(--text-secondary);
    margin-bottom: 0.75rem;
    padding-left: 2rem;
    position: relative;
}

.verification-info li::before {
    content: "✓";
    position: absolute;
    left: 0;
    top: 0;
    color: var(--primary-yellow);
    font-weight: bold;
    font-size: 1.1em;
}

/* Results Page Styles */
.latest-results {
    padding: 3rem 0;
    background: var(--bg-secondary);
}

.result-card {
    background: var(--bg-tertiary);
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(255, 235, 59, 0.1);
    padding: 2rem;
    margin-bottom: 2rem;
    border: 1px solid var(--border-primary);
    transition: all 0.3s ease;
}

.result-card:hover {
    border-color: var(--primary-yellow);
    box-shadow: 0 12px 30px rgba(255, 235, 59, 0.2);
}

.result-card.featured {
    border: 2px solid var(--primary-yellow);
    box-shadow: 0 12px 30px rgba(255, 235, 59, 0.3);
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.result-header h3 {
    color: var(--primary-yellow);
    font-size: 1.4em;
    font-weight: 600;
}

.draw-date {
    color: var(--text-secondary);
    font-weight: 500;
}

.verification-status {
    background: rgba(255, 235, 59, 0.2);
    color: var(--primary-yellow);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.9em;
    font-weight: 500;
    border: 1px solid var(--primary-yellow);
}

.winning-numbers {
    margin-bottom: 2rem;
}

.winning-numbers h4,
.winning-numbers h5 {
    color: var(--primary-yellow);
    margin-bottom: 1rem;
    font-weight: 600;
}

.numbers {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.number {
    background: var(--primary-yellow);
    color: var(--text-dark);
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.1em;
    border: 2px solid var(--dark-yellow);
    box-shadow: 0 2px 8px rgba(255, 235, 59, 0.3);
}

.supp-number {
    background: var(--secondary-yellow);
    color: var(--text-dark);
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 0.5rem;
    border: 2px solid var(--dark-yellow);
    box-shadow: 0 2px 8px rgba(255, 235, 59, 0.3);
}

.prize-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
    background: var(--bg-secondary);
    border-radius: 8px;
    overflow: hidden;
}

.prize-table th,
.prize-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--border-primary);
}

.prize-table th {
    background: var(--bg-tertiary);
    font-weight: 600;
    color: var(--primary-yellow);
}

.prize-table td {
    color: var(--text-secondary);
}

.prize-table tr:hover {
    background: var(--bg-tertiary);
}

.prize-table tr:hover td {
    color: var(--text-primary);
}

.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.jackpot-amount {
    background: #fff3cd;
    color: #856404;
    padding: 0.75rem;
    border-radius: 5px;
    font-weight: bold;
    text-align: center;
    margin-top: 1rem;
}

/* Verification Process */
.verification-process {
    padding: 3rem 0;
    background: #f8f9fa;
}

.verification-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.verification-step {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.verification-step h3 {
    color: #007bff;
    margin-bottom: 1rem;
    font-size: 1.3em;
}

/* Statistics */
.statistics {
    padding: 3rem 0;
}

.stats-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    margin-top: 2rem;
}

.probability-table table {
    width: 100%;
    border-collapse: collapse;
}

.probability-table th,
.probability-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.probability-table th {
    background: #007bff;
    color: white;
    font-weight: 600;
}

.probability-disclaimer {
    background: #fff3cd;
    border: 2px solid #ffeaa7;
    border-radius: 10px;
    padding: 1.5rem;
}

.probability-disclaimer h3 {
    color: #856404;
    margin-bottom: 1rem;
}

.probability-disclaimer ul {
    color: #856404;
}

.probability-disclaimer li {
    margin-bottom: 0.5rem;
}

/* Verification Contact */
.verification-contact {
    background: #e8f5e8;
    padding: 3rem 0;
    text-align: center;
}

.contact-details {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    display: inline-block;
    margin-top: 1rem;
    text-align: left;
}

.contact-details a {
    color: #007bff;
    text-decoration: none;
}

.contact-details a:hover {
    text-decoration: underline;
}

/* Responsible Gaming Page Styles */
.emergency-notice {
    background: #dc3545;
    color: white;
    padding: 1rem;
    border-radius: 5px;
    font-weight: bold;
    text-align: center;
    margin-top: 1rem;
}

.quick-help {
    padding: 3rem 0;
}

.help-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.help-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.help-card:hover {
    transform: translateY(-5px);
}

.help-card.urgent {
    border: 3px solid #dc3545;
    background: linear-gradient(135deg, #fff 0%, #ffe6e6 100%);
}

.help-card h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.3em;
}

.help-card.urgent h3 {
    color: #dc3545;
}

.help-button {
    background: #dc3545;
    color: white;
    padding: 1rem 2rem;
    text-decoration: none;
    border-radius: 25px;
    display: inline-block;
    margin: 1rem 0;
    font-weight: bold;
    transition: all 0.3s ease;
}

.help-button:hover {
    background: #c82333;
    transform: translateY(-2px);
}

.help-card:not(.urgent) .help-button {
    background: #007bff;
}

.help-card:not(.urgent) .help-button:hover {
    background: #0056b3;
}

/* Warning Signs */
.warning-signs {
    padding: 3rem 0;
    background: #f8f9fa;
}

.signs-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    margin-top: 2rem;
}

.signs-list {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.signs-list h3 {
    color: #dc3545;
    margin-bottom: 1.5rem;
    font-size: 1.4em;
}

.signs-list ul {
    list-style: none;
    padding: 0;
}

.signs-list li {
    padding: 0.75rem 0;
    border-bottom: 1px solid #eee;
    position: relative;
    padding-left: 2rem;
}

.signs-list li:before {
    content: "⚠️";
    position: absolute;
    left: 0;
    top: 0.75rem;
}

.help-message {
    background: #e8f5e8;
    padding: 2rem;
    border-radius: 10px;
    border-left: 4px solid #28a745;
}

.help-message h3 {
    color: #28a745;
    margin-bottom: 1rem;
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 1.5rem;
}

/* Self-Help Tools */
.self-help-tools {
    padding: 3rem 0;
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.tool-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-top: 4px solid #007bff;
}

.tool-card h3 {
    color: #007bff;
    margin-bottom: 1rem;
    font-size: 1.3em;
}

.tool-card ul {
    margin-top: 1rem;
}

.tool-card li {
    margin-bottom: 0.5rem;
    color: #6c757d;
}

/* BetStop Information */
.betstop-info {
    padding: 3rem 0;
    background: #f8f9fa;
}

.betstop-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    margin-top: 2rem;
}

.betstop-description {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.betstop-description h3,
.betstop-description h4 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.betstop-action {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    padding: 2rem;
    border-radius: 10px;
    text-align: center;
}

.betstop-action h3 {
    margin-bottom: 1rem;
}

.betstop-action .btn-primary {
    background: white;
    color: #dc3545;
    border: 2px solid white;
    font-weight: bold;
}

.betstop-action .btn-primary:hover {
    background: #f8f9fa;
    color: #c82333;
}

.small-text {
    font-size: 0.9em;
    opacity: 0.9;
    margin-top: 0.5rem;
}

/* Support Services */
.support-services {
    padding: 3rem 0;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.service-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #28a745;
}

.service-card h3 {
    color: #28a745;
    margin-bottom: 1rem;
    font-size: 1.3em;
}

.service-details {
    margin-top: 1rem;
}

.service-details p {
    margin-bottom: 0.5rem;
    color: #6c757d;
}

.service-details strong {
    color: #2c3e50;
}

.service-details a {
    color: #007bff;
    text-decoration: none;
}

.service-details a:hover {
    text-decoration: underline;
}

/* Family and Friends */
.family-friends {
    padding: 3rem 0;
    background: #f8f9fa;
}

.family-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    margin-top: 2rem;
}

.family-info {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.family-info h3,
.family-info h4 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.family-resources {
    background: #e8f5e8;
    padding: 2rem;
    border-radius: 10px;
    border-left: 4px solid #28a745;
}

.family-resources h3 {
    color: #28a745;
    margin-bottom: 1rem;
}

.family-resources ul {
    list-style: none;
    padding: 0;
}

.family-resources li {
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: white;
    border-radius: 5px;
}

/* Our Commitment */
.our-commitment {
    padding: 3rem 0;
}

.commitment-content {
    text-align: center;
}

.commitment-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.commitment-item {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.commitment-item h3 {
    color: #007bff;
    margin-bottom: 1rem;
    font-size: 1.3em;
}

/* Remember Section */
.remember-section {
    padding: 3rem 0;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    text-align: center;
}

.remember-content h2 {
    font-size: 2.5em;
    margin-bottom: 2rem;
}

.remember-points {
    margin: 2rem 0;
}

.remember-points p {
    font-size: 1.2em;
    margin-bottom: 1rem;
    opacity: 0.95;
}

.final-help-links {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
    margin-top: 2rem;
}

.help-link-large {
    background: white;
    color: #28a745;
    padding: 1rem 2rem;
    text-decoration: none;
    border-radius: 25px;
    font-weight: bold;
    font-size: 1.1em;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.help-link-large:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    background: #f8f9fa;
}

/* Terms and Privacy Page Styles */
.legal-details {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    margin: 1rem 0;
}

.legal-details p {
    margin-bottom: 0.5rem;
}

.restrictions-content,
.rules-content,
.claims-content,
.gaming-terms,
.privacy-content,
.use-content,
.sharing-content,
.security-content,
.rights-content {
    margin-top: 2rem;
}

.restrictions-content h3,
.rules-content h3,
.claims-content h3,
.gaming-terms h3,
.privacy-content h3,
.use-content h3,
.sharing-content h3,
.security-content h3,
.rights-content h3 {
    color: #2c3e50;
    margin: 2rem 0 1rem 0;
    font-size: 1.3em;
}

.restrictions-content ul,
.rules-content ul,
.claims-content ul,
.gaming-terms ul,
.privacy-content ul,
.use-content ul,
.sharing-content ul,
.security-content ul,
.rights-content ul {
    margin: 1rem 0;
    padding-left: 2rem;
}

.restrictions-content li,
.rules-content li,
.claims-content li,
.gaming-terms li,
.privacy-content li,
.use-content li,
.sharing-content li,
.security-content li,
.rights-content li {
    margin-bottom: 0.5rem;
    color: #6c757d;
}

.operator-info,
.age-restrictions,
.participation-rules,
.prize-claims,
.responsible-gaming-terms,
.privacy-data,
.regulatory-compliance,
.liability,
.changes-terms,
.governing-law,
.contact-info,
.information-collection,
.information-use,
.information-sharing,
.data-security,
.your-rights,
.cookie-policy,
.third-party-services,
.policy-changes,
.contact-privacy {
    padding: 2rem 0;
    border-bottom: 1px solid #eee;
}

.operator-info:last-child,
.contact-privacy:last-child {
    border-bottom: none;
}

.final-disclaimers {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 10px;
    margin-top: 2rem;
}

.disclaimers-content h2 {
    color: #2c3e50;
    margin-bottom: 2rem;
    text-align: center;
}

.disclaimer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.disclaimer-item {
    background: white;
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid #dc3545;
    text-align: center;
}

.disclaimer-item p {
    margin: 0;
    font-weight: 600;
    color: #2c3e50;
}

.help-reminder {
    background: #e8f5e8;
    padding: 1.5rem;
    border-radius: 8px;
    text-align: center;
    border: 2px solid #28a745;
}

.help-reminder p {
    margin: 0;
    color: #155724;
    font-weight: 600;
}

.help-reminder a {
    color: #007bff;
    text-decoration: none;
}

.help-reminder a:hover {
    text-decoration: underline;
}

/* Cookie Policy Specific Styles */
.cookie-types {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.cookie-type {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    border-left: 4px solid #007bff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.cookie-type h4 {
    color: #007bff;
    margin-bottom: 1rem;
}

.cookie-type p {
    color: #6c757d;
    margin: 0;
}

/* Responsible Gaming Reminder */
.responsible-gaming-reminder {
    background: #e8f5e8;
    padding: 2rem 0;
    text-align: center;
}

.reminder-content {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    max-width: 800px;
    margin: 0 auto;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.reminder-content h3 {
    color: #28a745;
    margin-bottom: 1rem;
    font-size: 1.5em;
}

.help-link {
    background: #28a745;
    color: white;
    padding: 0.75rem 1.5rem;
    text-decoration: none;
    border-radius: 25px;
    margin: 0 0.5rem;
    display: inline-block;
    transition: all 0.3s ease;
    font-weight: 500;
}

.help-link:hover {
    background: #219a52;
    transform: translateY(-2px);
}

/* Additional Mobile Responsive Styles */
@media (max-width: 768px) {

    .verification-content,
    .betstop-content,
    .family-content,
    .signs-content,
    .stats-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .steps-grid,
    .help-grid,
    .tools-grid,
    .services-grid,
    .commitment-grid {
        grid-template-columns: 1fr;
    }

    .result-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .numbers {
        justify-content: center;
    }

    .prize-table {
        font-size: 0.9em;
    }

    .prize-table th,
    .prize-table td {
        padding: 0.5rem;
    }

    .final-help-links {
        flex-direction: column;
        align-items: center;
    }

    .action-buttons {
        align-items: center;
    }

    .disclaimer-grid {
        grid-template-columns: 1fr;
    }

    .cookie-types {
        grid-template-columns: 1fr;
    }

    .remember-content h2 {
        font-size: 2em;
    }

    .help-link-large {
        font-size: 1em;
        padding: 0.75rem 1.5rem;
    }
}

@media (max-width: 480px) {
    .step-number {
        width: 40px;
        height: 40px;
        font-size: 1.2em;
    }

    .number {
        width: 35px;
        height: 35px;
        font-size: 1em;
    }

    .supp-number {
        width: 30px;
        height: 30px;
        font-size: 0.9em;
    }

    .help-button,
    .help-link,
    .help-link-large {
        padding: 0.75rem 1rem;
        font-size: 0.9em;
    }

    .modal-content {
        padding: 1.5rem;
    }

    .modal-buttons {
        gap: 0.5rem;
    }

    .legal-details {
        padding: 1rem;
    }

    .operator-info,
    .age-restrictions,
    .participation-rules,
    .prize-claims,
    .responsible-gaming-terms,
    .privacy-data,
    .regulatory-compliance,
    .liability,
    .changes-terms,
    .governing-law,
    .contact-info,
    .information-collection,
    .information-use,
    .information-sharing,
    .data-security,
    .your-rights,
    .cookie-policy,
    .third-party-services,
    .policy-changes,
    .contact-privacy {
        padding: 1.5rem 0;
    }
}

/* Mobile Menu Styles */
@media (max-width: 768px) {
    .nav-menu {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        flex-direction: column;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 1000;
    }

    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .nav-menu li {
        margin: 0;
        border-bottom: 1px solid #eee;
    }

    .nav-menu a {
        display: block;
        padding: 1rem;
        margin: 0;
        border-radius: 0;
    }
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem;
    border-radius: 5px;
    z-index: 10001;
    max-width: 300px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    animation: slideIn 0.3s ease;
}

.notification-success {
    background: #28a745;
    color: white;
}

.notification-info {
    background: #007bff;
    color: white;
}

.notification-warning {
    background: #ffc107;
    color: #212529;
}

.notification-error {
    background: #dc3545;
    color: white;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }

    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Cookie Preferences Modal Styles */
.cookie-option {
    margin: 1rem 0;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 5px;
}

.cookie-option label {
    display: flex;
    flex-direction: column;
    cursor: pointer;
}

.cookie-option input[type="checkbox"] {
    margin-bottom: 0.5rem;
    width: auto;
}

.cookie-option small {
    color: #6c757d;
    font-size: 0.875em;
}

/* Field Error Styles */
.field-error {
    color: #dc3545;
    font-size: 0.875em;
    margin-top: 0.25rem;
    display: block;
}

/* Focus Styles for Better Accessibility */
input:focus,
button:focus,
select:focus,
textarea:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* Skip Link Styles */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #000;
    color: #fff;
    padding: 8px;
    text-decoration: none;
    z-index: 10000;
    border-radius: 0 0 4px 4px;
}

.skip-link:focus {
    top: 6px;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}