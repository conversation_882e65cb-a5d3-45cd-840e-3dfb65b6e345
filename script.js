// Australian Lottery Website JavaScript
// Compliance with Google Ads Gambling Certification requirements

document.addEventListener('DOMContentLoaded', function () {
    // Initialize all functionality
    initializeAgeGate();
    initializeCookieConsent();
    initializeMobileMenu();
    initializeAccessibility();
    initializeFormValidation();

    // Check if user has already verified age
    if (!isAgeVerified()) {
        showAgeGate();
    } else {
        hideAgeGate();
    }

    // Show cookie consent if not already accepted
    if (!isCookieConsentGiven()) {
        showCookieConsent();
    }
});

// Age Gate Functionality
function initializeAgeGate() {
    const confirmBtn = document.getElementById('confirmAgeBtn');
    const denyBtn = document.getElementById('denyAgeBtn');

    if (confirmBtn) {
        confirmBtn.addEventListener('click', handleAgeConfirmation);
    }

    if (denyBtn) {
        denyBtn.addEventListener('click', handleAgeDenial);
    }
}

function showAgeGate() {
    const modal = document.getElementById('ageGateModal');
    if (modal) {
        modal.style.display = 'flex';
        modal.setAttribute('aria-hidden', 'false');

        // Focus on the confirm button for accessibility
        const confirmBtn = document.getElementById('confirmAgeBtn');
        if (confirmBtn) {
            setTimeout(() => confirmBtn.focus(), 100);
        }

        // Prevent scrolling on body
        document.body.style.overflow = 'hidden';
    }
}

function hideAgeGate() {
    const modal = document.getElementById('ageGateModal');
    if (modal) {
        modal.style.display = 'none';
        modal.setAttribute('aria-hidden', 'true');
        document.body.style.overflow = '';
    }
}

function handleAgeConfirmation() {
    // User confirms they are 18 or older
    setAgeVerified(true);
    hideAgeGate();

    // Show success message
    showNotification('Age verification successful. Welcome to our lottery service.', 'success');
}

function handleAgeDenial() {
    // User confirms they are under 18 - reload the page to show age gate again
    // Clear any stored verification
    sessionStorage.removeItem('ageVerified');

    // Show message and reload
    showNotification('You must be 18 or older to access this service.', 'warning');

    // Reload page after a short delay
    setTimeout(() => {
        window.location.reload();
    }, 2000);
}

function isAgeVerified() {
    return sessionStorage.getItem('ageVerified') === 'true';
}

function setAgeVerified(verified) {
    sessionStorage.setItem('ageVerified', verified.toString());
}

// Cookie Consent Functionality
function initializeCookieConsent() {
    const acceptAllBtn = document.getElementById('acceptAllCookies');
    const manageCookiesBtn = document.getElementById('manageCookies');

    if (acceptAllBtn) {
        acceptAllBtn.addEventListener('click', acceptAllCookies);
    }

    if (manageCookiesBtn) {
        manageCookiesBtn.addEventListener('click', showCookiePreferences);
    }
}

function showCookieConsent() {
    const banner = document.getElementById('cookieConsent');
    if (banner) {
        banner.style.display = 'block';
        banner.setAttribute('aria-hidden', 'false');
    }
}

function hideCookieConsent() {
    const banner = document.getElementById('cookieConsent');
    if (banner) {
        banner.style.display = 'none';
        banner.setAttribute('aria-hidden', 'true');
    }
}

function acceptAllCookies() {
    setCookieConsent({
        essential: true,
        functional: true,
        analytics: true,
        marketing: true
    });

    hideCookieConsent();
    showNotification('Cookie preferences saved. Thank you.', 'success');
}

function showCookiePreferences() {
    // Create modal for cookie preferences
    const modal = createCookiePreferencesModal();
    document.body.appendChild(modal);
    modal.style.display = 'flex';
}

function createCookiePreferencesModal() {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.id = 'cookiePreferencesModal';
    modal.setAttribute('role', 'dialog');
    modal.setAttribute('aria-labelledby', 'cookieModalTitle');

    modal.innerHTML = `
        <div class="modal-content">
            <h2 id="cookieModalTitle">Cookie Preferences</h2>
            <p>Choose which cookies you'd like to accept:</p>
            <form id="cookiePreferencesForm">
                <div class="cookie-option">
                    <label>
                        <input type="checkbox" checked disabled> Essential Cookies
                        <small>Required for basic website functionality</small>
                    </label>
                </div>
                <div class="cookie-option">
                    <label>
                        <input type="checkbox" id="functionalCookies"> Functional Cookies
                        <small>Remember your preferences and settings</small>
                    </label>
                </div>
                <div class="cookie-option">
                    <label>
                        <input type="checkbox" id="analyticsCookies"> Analytics Cookies
                        <small>Help us understand how you use our website</small>
                    </label>
                </div>
                <div class="cookie-option">
                    <label>
                        <input type="checkbox" id="marketingCookies"> Marketing Cookies
                        <small>Used for relevant advertisements</small>
                    </label>
                </div>
                <div class="modal-buttons">
                    <button type="submit" class="btn-primary">Save Preferences</button>
                    <button type="button" class="btn-secondary" onclick="closeCookiePreferences()">Cancel</button>
                </div>
            </form>
        </div>
    `;

    const form = modal.querySelector('#cookiePreferencesForm');
    form.addEventListener('submit', saveCookiePreferences);

    return modal;
}

function saveCookiePreferences(event) {
    event.preventDefault();

    const preferences = {
        essential: true, // Always true
        functional: document.getElementById('functionalCookies').checked,
        analytics: document.getElementById('analyticsCookies').checked,
        marketing: document.getElementById('marketingCookies').checked
    };

    setCookieConsent(preferences);
    closeCookiePreferences();
    hideCookieConsent();
    showNotification('Cookie preferences saved successfully.', 'success');
}

function closeCookiePreferences() {
    const modal = document.getElementById('cookiePreferencesModal');
    if (modal) {
        modal.remove();
    }
}

function isCookieConsentGiven() {
    return localStorage.getItem('cookieConsent') !== null;
}

function setCookieConsent(preferences) {
    localStorage.setItem('cookieConsent', JSON.stringify(preferences));
    localStorage.setItem('cookieConsentDate', new Date().toISOString());
}

// Mobile Menu Functionality
function initializeMobileMenu() {
    const toggle = document.querySelector('.mobile-menu-toggle');
    const menu = document.querySelector('.nav-menu');

    if (toggle && menu) {
        toggle.addEventListener('click', function () {
            menu.classList.toggle('active');
            toggle.setAttribute('aria-expanded',
                menu.classList.contains('active') ? 'true' : 'false'
            );
        });
    }
}

// Accessibility Features
function initializeAccessibility() {
    // Skip to main content link
    addSkipLink();

    // Keyboard navigation for modals
    document.addEventListener('keydown', handleKeyboardNavigation);

    // Focus management
    manageFocus();
}

function addSkipLink() {
    const skipLink = document.createElement('a');
    skipLink.href = '#main-content';
    skipLink.textContent = 'Skip to main content';
    skipLink.className = 'sr-only';
    skipLink.style.position = 'absolute';
    skipLink.style.top = '-40px';
    skipLink.style.left = '6px';
    skipLink.style.background = '#000';
    skipLink.style.color = '#fff';
    skipLink.style.padding = '8px';
    skipLink.style.textDecoration = 'none';
    skipLink.style.zIndex = '10000';

    skipLink.addEventListener('focus', function () {
        this.style.top = '6px';
    });

    skipLink.addEventListener('blur', function () {
        this.style.top = '-40px';
    });

    document.body.insertBefore(skipLink, document.body.firstChild);
}

function handleKeyboardNavigation(event) {
    // Escape key closes modals
    if (event.key === 'Escape') {
        const modals = document.querySelectorAll('.modal-overlay');
        modals.forEach(modal => {
            if (modal.style.display === 'flex') {
                if (modal.id === 'ageGateModal') {
                    // Don't allow closing age gate with escape
                    return;
                }
                modal.style.display = 'none';
            }
        });
    }
}

function manageFocus() {
    // Ensure focus stays within modals
    const modals = document.querySelectorAll('.modal-overlay');
    modals.forEach(modal => {
        modal.addEventListener('keydown', trapFocus);
    });
}

function trapFocus(event) {
    if (event.key !== 'Tab') return;

    const modal = event.currentTarget;
    const focusableElements = modal.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );

    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    if (event.shiftKey) {
        if (document.activeElement === firstElement) {
            lastElement.focus();
            event.preventDefault();
        }
    } else {
        if (document.activeElement === lastElement) {
            firstElement.focus();
            event.preventDefault();
        }
    }
}

// Form Validation
function initializeFormValidation() {
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', validateForm);
    });
}

function validateForm(event) {
    const form = event.target;
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            showFieldError(field, 'This field is required');
            isValid = false;
        } else {
            clearFieldError(field);
        }
    });

    // Specific validation for date fields
    const dateFields = form.querySelectorAll('input[type="date"]');
    dateFields.forEach(field => {
        if (field.value && !isValidDate(field.value)) {
            showFieldError(field, 'Please enter a valid date');
            isValid = false;
        }
    });

    if (!isValid) {
        event.preventDefault();
    }
}

function showFieldError(field, message) {
    clearFieldError(field);

    const error = document.createElement('div');
    error.className = 'field-error';
    error.textContent = message;
    error.style.color = '#dc3545';
    error.style.fontSize = '0.875em';
    error.style.marginTop = '0.25rem';

    field.parentNode.appendChild(error);
    field.setAttribute('aria-invalid', 'true');
    field.setAttribute('aria-describedby', error.id = 'error-' + Date.now());
}

function clearFieldError(field) {
    const existingError = field.parentNode.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }
    field.removeAttribute('aria-invalid');
    field.removeAttribute('aria-describedby');
}

function isValidDate(dateString) {
    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date);
}

// Utility Functions
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#28a745' : '#007bff'};
        color: white;
        padding: 1rem;
        border-radius: 5px;
        z-index: 10001;
        max-width: 300px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.remove();
    }, 5000);
}

// Play Now Modal (for demonstration)
function showPlayModal() {
    if (!isAgeVerified()) {
        showAgeGate();
        return;
    }

    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <h2>Play Lottery</h2>
            <p>This is a demonstration of the play functionality. In a real implementation, this would connect to the secure lottery system.</p>
            <div class="modal-buttons">
                <button class="btn-primary" onclick="this.closest('.modal-overlay').remove()">Close</button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    modal.style.display = 'flex';
}

// Note: Geolocation check removed to avoid browser permission requests
// Geographic targeting is handled through other means (IP-based, etc.)
