/* Additional Notion-style Black & Yellow Theme Styles */

/* Legal Framework and Regulation Cards */
.legal-framework,
.age-verification,
.transparency,
.verification-process,
.statistics,
.betstop-info,
.family-friends,
.our-commitment {
    background: var(--bg-secondary);
    padding: 3rem 0;
}

.legal-framework:nth-child(even),
.age-verification:nth-child(even),
.transparency:nth-child(even) {
    background: var(--bg-tertiary);
}

.regulation-card,
.transparency-item,
.verification-step,
.tool-card,
.service-card,
.commitment-item {
    background: var(--bg-tertiary);
    padding: 2rem;
    border-radius: 12px;
    border: 1px solid var(--border-primary);
    transition: all 0.3s ease;
}

.regulation-card:hover,
.transparency-item:hover,
.verification-step:hover,
.tool-card:hover,
.service-card:hover,
.commitment-item:hover {
    border-color: var(--primary-yellow);
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(255, 235, 59, 0.2);
}

.regulation-card h3,
.transparency-item h3,
.verification-step h3,
.tool-card h3,
.service-card h3,
.commitment-item h3 {
    color: var(--primary-yellow);
    margin-bottom: 1rem;
    font-weight: 600;
}

.regulation-card p,
.transparency-item p,
.verification-step p,
.tool-card p,
.service-card p,
.commitment-item p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Responsible Gaming Page Styles */
.emergency-notice {
    background: var(--primary-yellow);
    color: var(--text-dark);
    padding: 1rem;
    border-radius: 8px;
    font-weight: bold;
    text-align: center;
    margin-top: 1rem;
    border: 2px solid var(--dark-yellow);
}

.help-card {
    background: var(--bg-tertiary);
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(255, 235, 59, 0.1);
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid var(--border-primary);
}

.help-card:hover {
    transform: translateY(-8px);
    border-color: var(--primary-yellow);
    box-shadow: 0 12px 30px rgba(255, 235, 59, 0.2);
}

.help-card.urgent {
    border: 3px solid var(--primary-yellow);
    background: linear-gradient(135deg, var(--bg-tertiary) 0%, rgba(255, 235, 59, 0.1) 100%);
}

.help-card h3 {
    color: var(--primary-yellow);
    margin-bottom: 1rem;
    font-size: 1.3em;
    font-weight: 600;
}

.help-card.urgent h3 {
    color: var(--primary-yellow);
}

.help-button {
    background: var(--primary-yellow);
    color: var(--text-dark);
    padding: 1rem 2rem;
    text-decoration: none;
    border-radius: 25px;
    display: inline-block;
    margin: 1rem 0;
    font-weight: bold;
    transition: all 0.3s ease;
    border: 2px solid var(--dark-yellow);
}

.help-button:hover {
    background: var(--secondary-yellow);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(255, 235, 59, 0.3);
}

/* Warning Signs */
.signs-list {
    background: var(--bg-tertiary);
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(255, 235, 59, 0.1);
    border: 1px solid var(--border-primary);
}

.signs-list h3 {
    color: var(--primary-yellow);
    margin-bottom: 1.5rem;
    font-size: 1.4em;
    font-weight: 600;
}

.signs-list ul {
    list-style: none;
    padding: 0;
}

.signs-list li {
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-primary);
    position: relative;
    padding-left: 2rem;
    color: var(--text-secondary);
}

.signs-list li:before {
    content: "⚠️";
    position: absolute;
    left: 0;
    top: 0.75rem;
}

.help-message {
    background: var(--bg-tertiary);
    padding: 2rem;
    border-radius: 12px;
    border: 2px solid var(--primary-yellow);
}

.help-message h3 {
    color: var(--primary-yellow);
    margin-bottom: 1rem;
    font-weight: 600;
}

.help-message p {
    color: var(--text-secondary);
}

/* BetStop Information */
.betstop-description {
    background: var(--bg-tertiary);
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(255, 235, 59, 0.1);
    border: 1px solid var(--border-primary);
}

.betstop-description h3,
.betstop-description h4 {
    color: var(--primary-yellow);
    margin-bottom: 1rem;
    font-weight: 600;
}

.betstop-description p,
.betstop-description li {
    color: var(--text-secondary);
}

.betstop-action {
    background: linear-gradient(135deg, var(--primary-yellow) 0%, var(--secondary-yellow) 100%);
    color: var(--text-dark);
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    border: 2px solid var(--dark-yellow);
}

.betstop-action h3 {
    margin-bottom: 1rem;
    font-weight: 700;
}

.betstop-action .btn-primary {
    background: var(--text-dark);
    color: var(--primary-yellow);
    border: 2px solid var(--text-dark);
    font-weight: bold;
}

.betstop-action .btn-primary:hover {
    background: var(--bg-secondary);
    color: var(--primary-yellow);
}

/* Remember Section */
.remember-section {
    padding: 3rem 0;
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
    color: var(--text-primary);
    text-align: center;
    border-top: 3px solid var(--primary-yellow);
    border-bottom: 3px solid var(--primary-yellow);
}

.remember-content h2 {
    font-size: 2.5em;
    margin-bottom: 2rem;
    color: var(--primary-yellow);
    font-weight: 700;
}

.remember-points {
    margin: 2rem 0;
}

.remember-points p {
    font-size: 1.2em;
    margin-bottom: 1rem;
    opacity: 0.95;
    color: var(--text-secondary);
}

.help-link-large {
    background: var(--primary-yellow);
    color: var(--text-dark);
    padding: 1rem 2rem;
    text-decoration: none;
    border-radius: 25px;
    font-weight: bold;
    font-size: 1.1em;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 235, 59, 0.3);
    border: 2px solid var(--dark-yellow);
}

.help-link-large:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 235, 59, 0.4);
    background: var(--secondary-yellow);
}

/* Terms and Privacy Page Styles */
.legal-details {
    background: var(--bg-tertiary);
    padding: 1.5rem;
    border-radius: 8px;
    margin: 1rem 0;
    border: 1px solid var(--border-primary);
}

.legal-details p {
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

.legal-details strong {
    color: var(--text-primary);
}

.final-disclaimers {
    background: var(--bg-tertiary);
    padding: 2rem;
    border-radius: 12px;
    margin-top: 2rem;
    border: 2px solid var(--primary-yellow);
}

.disclaimers-content h2 {
    color: var(--primary-yellow);
    margin-bottom: 2rem;
    text-align: center;
    font-weight: 700;
}

.disclaimer-item {
    background: var(--bg-secondary);
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid var(--primary-yellow);
    text-align: center;
}

.disclaimer-item p {
    margin: 0;
    font-weight: 600;
    color: var(--text-primary);
}

.help-reminder {
    background: var(--bg-tertiary);
    padding: 1.5rem;
    border-radius: 8px;
    text-align: center;
    border: 2px solid var(--primary-yellow);
}

.help-reminder p {
    margin: 0;
    color: var(--text-primary);
    font-weight: 600;
}

.help-reminder a {
    color: var(--primary-yellow);
    text-decoration: none;
}

.help-reminder a:hover {
    text-decoration: underline;
}

/* Cookie Policy Specific Styles */
.cookie-type {
    background: var(--bg-tertiary);
    padding: 1.5rem;
    border-radius: 8px;
    border-left: 4px solid var(--primary-yellow);
    box-shadow: 0 4px 15px rgba(255, 235, 59, 0.1);
    border: 1px solid var(--border-primary);
}

.cookie-type h4 {
    color: var(--primary-yellow);
    margin-bottom: 1rem;
    font-weight: 600;
}

.cookie-type p {
    color: var(--text-secondary);
    margin: 0;
}

/* Responsible Gaming Reminder */
.responsible-gaming-reminder {
    background: var(--bg-tertiary);
    padding: 2rem 0;
    text-align: center;
    border-top: 2px solid var(--primary-yellow);
    border-bottom: 2px solid var(--primary-yellow);
}

.reminder-content {
    background: var(--bg-secondary);
    padding: 2rem;
    border-radius: 12px;
    max-width: 800px;
    margin: 0 auto;
    box-shadow: 0 8px 25px rgba(255, 235, 59, 0.1);
    border: 1px solid var(--border-primary);
}

.reminder-content h3 {
    color: var(--primary-yellow);
    margin-bottom: 1rem;
    font-size: 1.5em;
    font-weight: 600;
}

.reminder-content p {
    color: var(--text-secondary);
}

.help-link {
    background: var(--primary-yellow);
    color: var(--text-dark);
    padding: 0.75rem 1.5rem;
    text-decoration: none;
    border-radius: 25px;
    margin: 0 0.5rem;
    display: inline-block;
    transition: all 0.3s ease;
    font-weight: 500;
    border: 2px solid var(--dark-yellow);
}

.help-link:hover {
    background: var(--secondary-yellow);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 235, 59, 0.3);
}

/* Ensure all text on yellow backgrounds is dark - Additional elements */
.emergency-notice,
.emergency-notice *,
.help-button,
.help-button *,
.betstop-action,
.betstop-action h3,
.help-link-large,
.help-link-large *,
.help-link,
.help-link * {
    color: var(--text-dark) !important;
}